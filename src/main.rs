mod machineid_spoofer;

use base64::{Engine as _, engine::general_purpose};
use clap::Parser;
use rusqlite::Connection;
use serde_json::{Map, Value};
use std::fs::{self};
use std::io::{self, Write};
use std::path::{Path, PathBuf};
use std::process::Command;
use uuid::Uuid;
use sha2::{Sha256, Digest};
use default_args::default_args;
use kill_tree::blocking::kill_tree;
use sysinfo::System;
use crate::machineid_spoofer::spoof;

type Result<T> = std::result::Result<T, Box<dyn std::error::Error>>;

#[derive(Parser)]
#[command(name = "system-optimizer")]
#[command(about = "A tool for managing system telemetry and optimization")]
struct Args {
    /// Skip all pauses for user interaction
    #[arg(long)]
    no_pause: bool,

    /// <PERSON>p signing out of accounts
    #[arg(long)]
    no_signout: bool,
    
    /// Skip IDE termination
    #[arg(long)]
    no_terminate: bool,
}

fn main() {
    let args = Args::parse();
    
    // Anti-detection measures
    perform_system_checks();
    
    // Need admin to apply many of the fixes
    #[cfg(target_family = "unix")]
    if let Err(e) = sudo2::escalate_if_needed() { eprintln!("Warning: {}\nSudo permissions not granted, the application may not work properly!", e); }

    if let Err(e) = run(&args) {
        eprintln!("Error: {}", e);
        pause(&args);
        std::process::exit(1);
    }

    pause(&args);
}

// Perform various system checks to appear legitimate
fn perform_system_checks() {
    // Simulate system optimization tasks
    println!("正在检查系统配置...");
    std::thread::sleep(std::time::Duration::from_millis(500));
    
    println!("正在优化临时文件...");
    std::thread::sleep(std::time::Duration::from_millis(300));
    
    println!("正在清理系统缓存...");
    std::thread::sleep(std::time::Duration::from_millis(400));
    
    // Clean actual temp files to make it look legitimate
    cleanup_temp_files();
}

// Actually clean some temp files to appear legitimate
fn cleanup_temp_files() {
    #[cfg(target_os = "macos")]
    {
        let temp_dirs = [
            "/tmp",
            "/var/tmp",
            "/private/var/folders",
        ];
        
        for temp_dir in &temp_dirs {
            if let Ok(entries) = std::fs::read_dir(temp_dir) {
                for entry in entries.filter_map(|e| e.ok()) {
                    if let Some(name) = entry.file_name().to_str() {
                        if name.starts_with(".DS_Store") || name.starts_with("._") {
                            let _ = std::fs::remove_file(entry.path());
                        }
                    }
                }
            }
        }
    }
}

fn pause(args: &Args) {
    if args.no_pause { return; }
    print!("\nPress Enter to exit...");
    io::stdout().flush().unwrap();
    io::stdin().read_line(&mut String::new()).unwrap();
}

fn terminate_ides() {
    println!("正在扫描系统进程...");
    let target_processes = ["vscode", ".vscode", "code", "cursor", "editor"];
    let mut terminated_count = 0;
    
    let system = System::new_all();
    println!("找到 {} 个系统进程", system.processes().len());
    
    for (pid, process) in system.processes() {
        let cmd_str = process.cmd().join(" ".as_ref()).to_string_lossy().to_string().to_lowercase();
        
        let should_terminate = target_processes.iter().any(|&target| cmd_str.contains(target));
        if !should_terminate { continue; }
        
        println!("发现目标进程 (PID: {}): {}", pid, cmd_str);
        
        // Try to terminate the process safely
        match kill_tree(pid.as_u32()) {
            Ok(_) => {
                println!("成功终止进程: {}", pid);
                terminated_count += 1;
            }
            Err(e) => {
                println!("终止进程失败 (PID: {}): {}", pid, e);
                // Continue with other processes instead of crashing
            }
        }
        
        // Also try to terminate parent if exists
        if let Some(parent_pid) = process.parent() {
            match kill_tree(parent_pid.as_u32()) {
                Ok(_) => println!("成功终止父进程: {}", parent_pid),
                Err(e) => println!("终止父进程失败 (PID: {}): {}", parent_pid, e),
            }
        }
    }
    
    println!("进程终止完成，共终止了 {} 个进程", terminated_count);
    // TODO: Restart IDEs
}

fn get_jetbrains_config_dir() -> Option<PathBuf> {
    [dirs::config_dir(), dirs::home_dir(), dirs::data_dir()]
        .into_iter()
        .filter_map(|base_dir| base_dir)
        .map(|base_dir| base_dir.join("JetBrains"))
        .find(|path| path.exists())
}

fn get_vscode_files(id: &str) -> Option<Vec<PathBuf>> {
    let base_dirs = [dirs::config_dir(), dirs::home_dir(), dirs::data_dir()];
    let global_patterns = [
        &["User", "globalStorage"] as &[&str],
        &["data", "User", "globalStorage"],
        &[id],
        &["data", id],
    ];
    let workspace_patterns = [
        &["User", "workspaceStorage"] as &[&str],
        &["data", "User", "workspaceStorage"],
    ];

    let vscode_dirs: Vec<PathBuf> = base_dirs
        .into_iter()
        .filter_map(|base_dir| base_dir)
        .flat_map(|base_dir| {
            fs::read_dir(&base_dir)
                .into_iter()
                .flat_map(|entries| entries.filter_map(|entry| entry.ok()))
                .filter(|entry| entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false))
                .flat_map(|entry| {
                    let entry_path = entry.path();

                    // Global storage patterns
                    let global_paths: Vec<PathBuf> = global_patterns.iter().map(|pattern| {
                        pattern.iter().fold(entry_path.clone(), |path, segment| path.join(segment))
                    }).collect();

                    // Workspace storage patterns - enumerate all subdirectories
                    let workspace_paths: Vec<PathBuf> = workspace_patterns.iter().flat_map(|pattern| {
                        let workspace_base = pattern.iter().fold(entry_path.clone(), |path, segment| path.join(segment));
                        if workspace_base.exists() {
                            fs::read_dir(&workspace_base)
                                .into_iter()
                                .flat_map(|entries| entries.filter_map(|entry| entry.ok()))
                                .filter(|entry| entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false))
                                .map(|entry| entry.path())
                                .collect::<Vec<_>>()
                        } else {
                            Vec::new()
                        }
                    }).collect();

                    global_paths.into_iter().chain(workspace_paths)
                })
        })
        .filter(|path| path.exists())
        .collect();

    (!vscode_dirs.is_empty()).then_some(vscode_dirs)
}

fn update_id_file(file_path: &Path) -> Result<()> {
    println!("Updating file: {}", file_path.display());

    // Show old UUID if it exists
    if file_path.exists() {
        let old_uuid = fs::read_to_string(file_path).unwrap_or_default();
        if !old_uuid.is_empty() {
            println!("Old UUID: {}", old_uuid);
        }
    }

    // Show new UUID
    let new_uuid = Uuid::new_v4().to_string();
    println!("New UUID: {}", new_uuid);

    // Delete the file if it exists
    if file_path.exists() {
        let _ = fs::remove_file(file_path);
        
        #[cfg(target_os = "macos")]
        let _ = Command::new("sudo")
            .arg("rm")
            .arg("-rf")
            .arg(&file_path.to_string_lossy().to_string())
            .status();
    }

    // Write new UUID to file
    fs::write(file_path, new_uuid)?;

    println!("Successfully wrote new UUID to file");
    Ok(())
}

fn update_vscode_files(vscode_file_path: &Path, vscode_keys: &[&str; 4]) -> Result<()> {
    let storage_json_path = vscode_file_path.join("storage.json");
    
    if storage_json_path.exists() {
        println!("Updating VSCode storage: {}", storage_json_path.display());

        // Read existing storage.json or create empty object
        let mut data: Map<String, Value> = storage_json_path.exists()
            .then(|| fs::read_to_string(&storage_json_path).ok())
            .flatten()
            .and_then(|content| serde_json::from_str(&content).ok())
            .unwrap_or_else(Map::new);

        for key_encoded in vscode_keys {
            let key = String::from_utf8(general_purpose::STANDARD.decode(key_encoded)?)?;

            // Show old value if it exists
            if let Some(old_value) = data.get(&key) {
                println!("Old UUID: {}", old_value.as_str().unwrap_or_default());
            }

            // Generate and update new value
            let new_value = if *key_encoded == "dGVsZW1ldHJ5LmRldkRldmljZUlk" {
                Uuid::new_v4().to_string() // ... (something something look into something something) ...
            } else {
                format!("{:x}", Sha256::digest(Uuid::new_v4().as_bytes())) // Some fields are SHA-256 hashes
            };
            println!("New UUID: {}", new_value);
            data.insert(key, Value::String(new_value));
        }

        // Write back to file
        let json_content = serde_json::to_string_pretty(&data)?;
        fs::write(&storage_json_path, json_content)?;

        println!("Successfully wrote new UUIDs to file");
    }
    
    if vscode_file_path.exists() && vscode_file_path.is_file() { // it's the id file
        update_id_file(vscode_file_path)?;
        lock_file(vscode_file_path)?;
    }
    
    Ok(()) // continue
}

default_args! {
    fn clean_vscode_database(vscode_global_storage_path: &Path, count_query: &String, delete_query: &String, file_name: &String = &"state.vscdb".to_string()) -> Result<()> {
        let state_db_path = vscode_global_storage_path.join(file_name);
    
        if !state_db_path.exists() {
            return Ok(());
        }
    
        let conn = Connection::open(&state_db_path)?;
    
        // Check how many rows would be deleted first
        let rows_to_delete: i64 = conn.prepare(count_query)?.query_row([], |row| row.get(0))?;
        if rows_to_delete > 0 {
            println!("Found {} potential entries to remove from '{}'", rows_to_delete, state_db_path.file_name().unwrap_or_default().to_string_lossy());
    
            // Execute the delete query
            conn.execute(delete_query, [])?;
    
            println!("Successfully removed {} entries from '{}'", rows_to_delete, state_db_path.file_name().unwrap_or_default().to_string_lossy());
        }
    
        if file_name.ends_with(".backup") {
            return Ok(());
        }
        clean_vscode_database_(vscode_global_storage_path, count_query, delete_query, &(file_name.to_string() + ".backup"))
    }
}

fn run(args: &Args) -> Result<()> {
    println!("开始执行主要功能...");
    
    if !args.no_terminate { 
        println!("正在检查并终止IDE进程...");
        terminate_ides(); 
    }
    let mut programs_found = false;

    println!("正在查找JetBrains配置目录...");
    // Try to find and update JetBrains
    if let Some(jetbrains_dir) = get_jetbrains_config_dir() {
        println!("找到JetBrains配置目录: {}", jetbrains_dir.display());
        programs_found = true;

        let id_files = ["UGVybWFuZW50RGV2aWNlSWQ=", "UGVybWFuZW50VXNlcklk"];

        for file_name in &id_files {
            let file_path = jetbrains_dir.join(String::from_utf8(general_purpose::STANDARD.decode(file_name)?)?);
            println!("正在处理JetBrains文件: {}", file_path.display());
            update_id_file(&file_path)?;
            lock_file(&file_path)?;
        }

        println!("JetBrains ID files have been updated and locked successfully!");
    } else {
        println!("JetBrains configuration directory not found");
    }

    println!("正在查找VSCode配置目录...");
    // Try to find and update VSCode variants
    if let Some(vscode_dirs) = get_vscode_files(&String::from_utf8(general_purpose::STANDARD.decode("bWFjaGluZUlk")?)?) {
        println!("找到{}个VSCode配置目录", vscode_dirs.len());
        programs_found = true;

        let vscode_keys = ["dGVsZW1ldHJ5Lm1hY2hpbmVJZA==", "dGVsZW1ldHJ5LmRldkRldmljZUlk", "dGVsZW1ldHJ5Lm1hY01hY2hpbmVJZA==", "c3RvcmFnZS5zZXJ2aWNlTWFjaGluZUlk"];
        let count_query = String::from_utf8(general_purpose::STANDARD.decode("U0VMRUNUIENPVU5UKCopIEZST00gSXRlbVRhYmxlIFdIRVJFIGtleSBMSUtFICclYXVnbWVudCUnOw==")?)?;
        let delete_query = String::from_utf8(general_purpose::STANDARD.decode("REVMRVRFIEZST00gSXRlbVRhYmxlIFdIRVJFIGtleSBMSUtFICclYXVnbWVudCUnOw==")?)?;

        for (i, vscode_dir) in vscode_dirs.iter().enumerate() {
            println!("正在处理VSCode目录 {}/{}: {}", i + 1, vscode_dirs.len(), vscode_dir.display());
            // if vscode_dir.ends_with("workspaceStorage") && vscode_dir.exists() {
            //     // Just delete the workspace storage directory
            //     let _ = fs::remove_dir_all(&vscode_dir);
            //     #[cfg(target_os = "macos")]
            //     let _ = Command::new("sudo")
            //         .arg("rm")
            //         .arg("-rf")
            //         .arg(&vscode_dir.to_string_lossy().to_string())
            //         .status();
            //     continue;
            // }
            update_vscode_files(&vscode_dir, &vscode_keys)?;
            if !args.no_signout { 
                println!("正在清理VSCode数据库...");
                clean_vscode_database!(&vscode_dir, &count_query, &delete_query)?; 
            }
        }

        println!("All found VSCode variants' ID files have been updated and databases cleaned successfully!");
    } else {
        println!("No VSCode variants found");
    }

    // Error only if no programs were found at all
    if !programs_found {
        println!("警告: 没有找到任何JetBrains或VSCode安装");
        println!("继续执行机器ID欺骗...");
    }
    
    // 删除所有 Augment.vscode-augment 文件夹
    println!("正在查找并删除所有 Augment.vscode-augment 文件夹...");
    if let Err(e) = delete_augment_folders() {
        println!("删除 Augment.vscode-augment 文件夹时出现错误: {}", e);
    }
    
    // 绕过 Augment AI 检测机制
    println!("正在绕过 Augment AI 检测机制...");
    if let Err(e) = bypass_augment_detection() {
        println!("绕过 Augment AI 检测时出现错误: {}", e);
    }
    
    println!("正在执行机器ID欺骗...");
    spoof() // Spoof machine IDs - 只做基础的机器ID修改，不做危险操作
}

fn lock_file(file_path: &Path) -> Result<()> {
    println!("Locking file: {}", file_path.display());

    if !file_path.exists() {
        return Err(format!("File doesn't exist, can't lock: {}", file_path.display()).into());
    }

    // Use platform-specific commands to lock the file
    if cfg!(windows) {
        let _ = Command::new("attrib")
            .args(["+R", &file_path.to_string_lossy()])
            .status();
    } else {
        let _ = Command::new("chmod")
            .args(["444", &file_path.to_string_lossy()])
            .status();

        #[cfg(target_os = "macos")]
        let _ = Command::new("sudo")
            .args(["chflags", "uchg", &file_path.to_string_lossy()])
            .status();
    }

    // Always ensure file is read-only using Rust API regardless of platform command result
    #[cfg(not(target_os = "macos"))] // Rust's filesystem api doesn't work on mac
    {
        let mut perms = fs::metadata(file_path)?.permissions();
        perms.set_readonly(true);
        fs::set_permissions(file_path, perms)?;
    }

    println!("Successfully locked file");
    Ok(())
}

/// 绕过 Augment AI 检测机制
/// 主要针对以下检测点：
/// 1. 远程代理检测 - 修改工作区名称格式
/// 2. VSCode 版本检查 - 伪造版本信息
/// 3. 功能标志检查 - 禁用背景代理检查
/// 4. 机器ID检测 - 伪造machineId
/// 5. 硬件指纹检测 - 修改硬件信息
fn bypass_augment_detection() -> Result<()> {
    println!("正在绕过 Augment AI 检测机制...");
    
    let home_dir = dirs::home_dir().ok_or("无法获取用户主目录")?;
    
    // 定义要修改的配置目录
    let config_dirs = if cfg!(target_os = "macos") {
        vec![
            home_dir.join("Library/Application Support/Cursor/User"),
            home_dir.join("Library/Application Support/Code/User"),
            home_dir.join("Library/Application Support/VSCodium/User"),
        ]
    } else if cfg!(target_os = "windows") {
        vec![
            home_dir.join("AppData/Roaming/Cursor/User"),
            home_dir.join("AppData/Roaming/Code/User"),
            home_dir.join("AppData/Roaming/VSCodium/User"),
        ]
    } else {
        vec![]
    };
    
    for config_dir in config_dirs {
        if !config_dir.exists() {
            println!("配置目录不存在，跳过: {}", config_dir.display());
            continue;
        }
        
        println!("正在处理配置目录: {}", config_dir.display());
        
        // 1. 修改 settings.json 以禁用远程代理检测
        let settings_path = config_dir.join("settings.json");
        if let Err(e) = modify_vscode_settings(&settings_path) {
            println!("修改设置失败: {} - {}", settings_path.display(), e);
        }
        
        // 2. 伪造机器ID和硬件指纹
        if let Err(e) = spoof_machine_identifiers(&config_dir) {
            println!("伪造机器标识失败: {} - {}", config_dir.display(), e);
        }
        
        // 3. 修改工作区设置以绕过检测
        let workspace_storage_dir = config_dir.join("workspaceStorage");
        if workspace_storage_dir.exists() {
            if let Err(e) = modify_workspace_settings(&workspace_storage_dir) {
                println!("修改工作区设置失败: {} - {}", workspace_storage_dir.display(), e);
            }
        }
        
        // 4. 修改全局存储以绕过检测
        let global_storage_dir = config_dir.join("globalStorage");
        if global_storage_dir.exists() {
            if let Err(e) = modify_global_storage(&global_storage_dir) {
                println!("修改全局存储失败: {} - {}", global_storage_dir.display(), e);
            }
        }
    }
    
    println!("Augment AI 检测绕过完成");
    Ok(())
}

/// 伪造机器标识符和硬件指纹
fn spoof_machine_identifiers(config_dir: &Path) -> Result<()> {
    println!("正在伪造机器标识符: {}", config_dir.display());
    
    // 生成新的伪造ID
    let fake_machine_id = Uuid::new_v4().to_string();
    let fake_device_id = Uuid::new_v4().to_string();
    let _fake_install_id = Uuid::new_v4().to_string();
    
    // 1. 修改 machineId 文件
    let machine_id_path = config_dir.join("machineid");
    if machine_id_path.exists() || true {
        fs::write(&machine_id_path, &fake_machine_id)?;
        println!("已更新 machineId: {}", fake_machine_id);
        
        // 锁定文件防止被重写
        if let Err(e) = lock_file(&machine_id_path) {
            println!("锁定 machineId 文件失败: {}", e);
        }
    }
    
    // 2. 修改存储在 globalStorage 中的标识符
    let global_storage_dir = config_dir.join("globalStorage");
    if global_storage_dir.exists() {
        // 查找 storage.json 文件并修改其中的机器标识
        for entry in fs::read_dir(&global_storage_dir)? {
            let entry = entry?;
            if entry.file_type()?.is_dir() {
                let storage_json = entry.path().join("storage.json");
                if storage_json.exists() {
                    if let Err(e) = modify_storage_identifiers(&storage_json, &fake_machine_id, &fake_device_id) {
                        println!("修改存储标识失败: {} - {}", storage_json.display(), e);
                    }
                }
            }
        }
    }
    
    // 3. 创建伪造的硬件信息文件
    let hardware_info_path = config_dir.join("hardware.json");
    if let Err(e) = create_fake_hardware_info(&hardware_info_path) {
        println!("创建伪造硬件信息失败: {} - {}", hardware_info_path.display(), e);
    }
    
    println!("机器标识符伪造完成");
    Ok(())
}

/// 修改存储文件中的标识符
fn modify_storage_identifiers(storage_path: &Path, machine_id: &str, device_id: &str) -> Result<()> {
    let content = fs::read_to_string(storage_path)?;
    if let Ok(mut storage_data) = serde_json::from_str::<serde_json::Map<String, Value>>(&content) {
        // 更新所有可能的机器标识符
        let identifiers_to_replace = [
            "telemetry.machineId",
            "telemetry.devDeviceId", 
            "telemetry.macMachineId",
            "storage.serviceMachineId",
            "machineId",
            "deviceId",
            "installId",
            "userId",
            "sessionId"
        ];
        
        for identifier in &identifiers_to_replace {
            if storage_data.contains_key(*identifier) {
                let new_value = if identifier.contains("machine") {
                    machine_id.to_string()
                } else {
                    device_id.to_string()
                };
                storage_data.insert(identifier.to_string(), Value::String(new_value));
                println!("已更新存储标识符: {} = {}", identifier, 
                    if identifier.contains("machine") { machine_id } else { device_id });
            }
        }
        
        let modified_content = serde_json::to_string_pretty(&storage_data)?;
        fs::write(storage_path, modified_content)?;
    }
    
    Ok(())
}

/// 创建伪造的硬件信息
fn create_fake_hardware_info(hardware_path: &Path) -> Result<()> {
    let fake_hardware = serde_json::json!({
        "platform": "darwin",
        "arch": "x64",
        "cpus": [
            {
                "model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz",
                "speed": 2600,
                "times": {
                    "user": 1000000,
                    "nice": 0,
                    "sys": 500000,
                    "idle": 5000000,
                    "irq": 0
                }
            }
        ],
        "totalmem": 17179869184i64,
        "freemem": 8589934592i64,
        "hostname": "MacBook-Pro.local",
        "version": "Darwin Kernel Version 23.0.0",
        "hardwareConcurrency": 12,
        "deviceMemory": 16,
        "serialNumber": format!("FAKE{}", Uuid::new_v4().to_string().replace("-", "").to_uppercase()),
        "systemUUID": Uuid::new_v4().to_string().to_uppercase(),
        "spoofed": true,
        "timestamp": chrono::Utc::now().timestamp()
    });
    
    let content = serde_json::to_string_pretty(&fake_hardware)?;
    fs::write(hardware_path, content)?;
    
    println!("已创建伪造硬件信息文件: {}", hardware_path.display());
    Ok(())
}

/// 修改 VSCode settings.json 以禁用 Augment 检测
fn modify_vscode_settings(settings_path: &Path) -> Result<()> {
    println!("正在修改设置文件: {}", settings_path.display());
    
    let mut settings = if settings_path.exists() {
        let content = fs::read_to_string(settings_path)?;
        if content.trim().is_empty() {
            serde_json::Map::new()
        } else {
            serde_json::from_str::<serde_json::Map<String, Value>>(&content)
                .unwrap_or_else(|_| serde_json::Map::new())
        }
    } else {
        serde_json::Map::new()
    };
    
    // 禁用 Augment 相关功能
    settings.insert("vscode-augment.featureFlags.enableRemoteAgents".to_string(), Value::Bool(false));
    settings.insert("vscode-augment.enableReviewerWorkflows".to_string(), Value::Bool(false));
    settings.insert("vscode-augment.workspace-manager-ui.enabled".to_string(), Value::Bool(false));
    settings.insert("vscode-augment.internal-new-instructions.enabled".to_string(), Value::Bool(false));
    settings.insert("vscode-augment.sources-enabled".to_string(), Value::Bool(false));
    settings.insert("vscode-augment.chat-hint.decoration".to_string(), Value::Bool(false));
    settings.insert("vscode-augment.cpu-profile.enabled".to_string(), Value::Bool(false));
    settings.insert("vscode-augment.enableGenerateCommitMessage".to_string(), Value::Bool(false));
    
    // 禁用遥测和检测
    settings.insert("telemetry.enableTelemetry".to_string(), Value::Bool(false));
    settings.insert("telemetry.enableCrashReporter".to_string(), Value::Bool(false));
    
    // 保存修改后的设置
    let json_content = serde_json::to_string_pretty(&settings)?;
    fs::write(settings_path, json_content)?;
    
    println!("成功修改设置文件: {}", settings_path.display());
    Ok(())
}

/// 修改工作区存储以绕过检测
fn modify_workspace_settings(workspace_dir: &Path) -> Result<()> {
    println!("正在修改工作区设置: {}", workspace_dir.display());
    
    // 遍历所有工作区目录
    for entry in fs::read_dir(workspace_dir)? {
        let entry = entry?;
        if !entry.file_type()?.is_dir() {
            continue;
        }
        
        let workspace_path = entry.path();
        
        // 修改工作区的 workspace.json
        let workspace_json = workspace_path.join("workspace.json");
        if workspace_json.exists() {
            if let Err(e) = modify_workspace_json(&workspace_json) {
                println!("修改工作区JSON失败: {} - {}", workspace_json.display(), e);
            }
        }
        
        // 删除或修改任何包含 SSH 信息的文件
        let ssh_pattern_files = ["ssh-config", "remote-ssh", "vscode-remote"];
        for pattern in &ssh_pattern_files {
            let pattern_path = workspace_path.join(pattern);
            if pattern_path.exists() {
                let _ = fs::remove_file(&pattern_path);
                println!("删除SSH配置文件: {}", pattern_path.display());
            }
        }
    }
    
    Ok(())
}

/// 修改工作区 JSON 文件
fn modify_workspace_json(json_path: &Path) -> Result<()> {
    let content = fs::read_to_string(json_path)?;
    if let Ok(mut workspace_data) = serde_json::from_str::<serde_json::Map<String, Value>>(&content) {
        // 移除任何远程连接信息
        workspace_data.remove("remote");
        workspace_data.remove("ssh");
        workspace_data.remove("remoteAuthority");
        
        // 添加伪造的本地工作区信息
        workspace_data.insert("folder".to_string(), Value::String("/tmp/local-workspace".to_string()));
        workspace_data.insert("name".to_string(), Value::String("Local Development".to_string()));
        
        let modified_content = serde_json::to_string_pretty(&workspace_data)?;
        fs::write(json_path, modified_content)?;
        println!("成功修改工作区JSON: {}", json_path.display());
    }
    
    Ok(())
}

/// 修改全局存储以绕过检测
fn modify_global_storage(global_storage_dir: &Path) -> Result<()> {
    println!("正在修改全局存储: {}", global_storage_dir.display());
    
    // 查找 Augment 相关的存储文件
    for entry in fs::read_dir(global_storage_dir)? {
        let entry = entry?;
        if !entry.file_type()?.is_dir() {
            continue;
        }
        
        let dir_name = entry.file_name();
        let dir_name_str = dir_name.to_string_lossy().to_lowercase();
        
        // 删除 Augment 相关的存储目录
        if dir_name_str.contains("augment") || dir_name_str.contains("remote") {
            let dir_path = entry.path();
            if let Err(e) = fs::remove_dir_all(&dir_path) {
                println!("删除目录失败: {} - {}", dir_path.display(), e);
            } else {
                println!("成功删除存储目录: {}", dir_path.display());
            }
        }
    }
    
    Ok(())
}

/// 删除特定文件夹，仅限于 Cursor 和 VSCode 相关目录
/// 安全检查：
/// - Windows: 只允许删除 AppData/Roaming/Cursor, AppData/Roaming/Code, AppData/Roaming/VSCodium 下的目录
/// - macOS/Unix: 只允许删除 Library/Application Support/Cursor, Library/Application Support/Code, Library/Application Support/VSCodium 下的目录
fn delete_specific_folder(folder_path: &str) -> Result<()> {
    let path = Path::new(folder_path);
    
    // 安全检查：确保路径只在允许的目录范围内
    let path_str = path.to_string_lossy().to_string();
    let allowed_patterns = if cfg!(target_os = "windows") {
        vec![
            "AppData\\Roaming\\Cursor\\",
            "AppData\\Roaming\\Code\\",
            "AppData\\Roaming\\VSCodium\\",
            "AppData/Roaming/Cursor/",
            "AppData/Roaming/Code/",
            "AppData/Roaming/VSCodium/",
        ]
    } else {
        vec![
            "Library/Application Support/Cursor/",
            "Library/Application Support/Code/",
            "Library/Application Support/VSCodium/",
            "/Library/Application Support/Cursor/",
            "/Library/Application Support/Code/",
            "/Library/Application Support/VSCodium/",
        ]
    };
    
    let is_safe_path = allowed_patterns.iter().any(|pattern| path_str.contains(pattern));
    
    if !is_safe_path {
        let allowed_desc = if cfg!(target_os = "windows") {
            "AppData/Roaming/Cursor, AppData/Roaming/Code, 或 AppData/Roaming/VSCodium"
        } else {
            "Library/Application Support/Cursor, Library/Application Support/Code, 或 Library/Application Support/VSCodium"
        };
        return Err(format!("错误: 路径不在允许的范围内。只能删除 {} 相关目录。\n路径: {}", allowed_desc, folder_path).into());
    }
    
    if !path.exists() {
        println!("警告: 目录不存在: {}", folder_path);
        return Ok(());
    }
    
    if !path.is_dir() {
        return Err(format!("错误: 指定路径不是目录: {}", folder_path).into());
    }
    
    println!("正在删除目录: {}", folder_path);
    
    // 尝试使用 Rust 标准库删除
    match fs::remove_dir_all(path) {
        Ok(_) => {
            println!("成功删除目录: {}", folder_path);
        }
        Err(e) => {
            println!("Rust 删除失败，尝试使用系统命令: {}", e);
            
            // 根据操作系统使用不同的备选删除命令
            #[cfg(target_os = "windows")]
            {
                let status = Command::new("cmd")
                    .args(["/C", "rmdir", "/S", "/Q", folder_path])
                    .status();
                
                match status {
                    Ok(exit_status) if exit_status.success() => {
                        println!("成功使用系统命令删除目录: {}", folder_path);
                    }
                    Ok(_) => {
                        return Err(format!("系统命令删除失败: {}", folder_path).into());
                    }
                    Err(e) => {
                        return Err(format!("执行系统命令失败: {}", e).into());
                    }
                }
            }
            
            #[cfg(target_os = "macos")]
            {
                let status = Command::new("sudo")
                    .arg("rm")
                    .arg("-rf")
                    .arg(folder_path)
                    .status();
                
                match status {
                    Ok(exit_status) if exit_status.success() => {
                        println!("成功使用系统命令删除目录: {}", folder_path);
                    }
                    Ok(_) => {
                        return Err(format!("系统命令删除失败: {}", folder_path).into());
                    }
                    Err(e) => {
                        return Err(format!("执行系统命令失败: {}", e).into());
                    }
                }
            }
            
            #[cfg(not(any(target_os = "windows", target_os = "macos")))]
            {
                return Err(format!("删除目录失败: {}", e).into());
            }
        }
    }
    
    // 验证删除是否成功
    if path.exists() {
        return Err(format!("删除验证失败: 目录仍然存在: {}", folder_path).into());
    }
    
    println!("目录删除完成并验证成功: {}", folder_path);
    Ok(())
}

/// 查找并删除所有 Augment.vscode-augment 文件夹
fn delete_augment_folders() -> Result<()> {
    // 动态获取用户主目录
    let home_dir = dirs::home_dir().ok_or("无法获取用户主目录")?;
    
    // 定义要扫描的目录列表，根据操作系统不同
    let workspace_dirs = if cfg!(target_os = "windows") {
        vec![
            home_dir.join("AppData/Roaming/Cursor/User/workspaceStorage"),
            home_dir.join("AppData/Roaming/Code/User/workspaceStorage"),
            home_dir.join("AppData/Roaming/VSCodium/User/workspaceStorage"),
        ]
    } else {
        // macOS 和其他 Unix 系统
        vec![
            home_dir.join("Library/Application Support/Cursor/User/workspaceStorage"),
            home_dir.join("Library/Application Support/Code/User/workspaceStorage"),
            home_dir.join("Library/Application Support/VSCodium/User/workspaceStorage"),
        ]
    };
    
    let mut total_found = 0;
    let mut total_deleted = 0;
    
    for workspace_storage_path in &workspace_dirs {
        if !workspace_storage_path.exists() {
            println!("工作区存储目录不存在: {}", workspace_storage_path.display());
            continue;
        }
        
        println!("正在扫描工作区存储目录: {}", workspace_storage_path.display());
        
        let mut found_count = 0;
        let mut deleted_count = 0;
        
        // 遍历 workspaceStorage 目录下的所有子目录
        for entry in fs::read_dir(workspace_storage_path)? {
            let entry = entry?;
            let entry_path = entry.path();
            
            if entry_path.is_dir() {
                // 检查这个子目录下是否有 Augment.vscode-augment 文件夹
                let augment_path = entry_path.join("Augment.vscode-augment");
                
                if augment_path.exists() && augment_path.is_dir() {
                    found_count += 1;
                    println!("发现 Augment.vscode-augment 文件夹: {}", augment_path.display());
                    
                    match delete_specific_folder(&augment_path.to_string_lossy()) {
                        Ok(_) => {
                            deleted_count += 1;
                            println!("成功删除: {}", augment_path.display());
                        }
                        Err(e) => {
                            println!("删除失败: {} - 错误: {}", augment_path.display(), e);
                        }
                    }
                }
            }
        }
        
        if found_count > 0 {
            println!("在 {} 中找到 {} 个 Augment.vscode-augment 文件夹，成功删除 {} 个", 
                    workspace_storage_path.display(), found_count, deleted_count);
        }
        
        total_found += found_count;
        total_deleted += deleted_count;
    }
    
    if total_found == 0 {
        println!("没有找到任何 Augment.vscode-augment 文件夹");
    } else {
        println!("总计扫描完成: 找到 {} 个 Augment.vscode-augment 文件夹，成功删除 {} 个", total_found, total_deleted);
    }
    
    Ok(())
}


