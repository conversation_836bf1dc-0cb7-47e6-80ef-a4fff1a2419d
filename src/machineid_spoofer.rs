pub fn spoof() -> Result<(), Box<dyn std::error::Error>> {
    println!("Spoofing machine IDs...");
    #[cfg(target_os = "windows")]
    return spoof_windows();
    #[cfg(target_os = "linux")]
    return spoof_linux();
    #[cfg(target_os = "macos")]
    return spoof_macos();
}

// Enhanced Windows machine ID spoofing to counter Augment detection
#[cfg(target_os = "windows")]
fn spoof_windows() -> Result<(), Box<dyn std::error::Error>> {
    use winreg::enums::{HKEY_LOCAL_MACHINE, KEY_READ, KEY_WRITE};
    use winreg::RegKey;
    use uuid::Uuid;

    println!("正在修改Windows机器标识符...");

    // Spoof the main MachineGuid that Augment checks
    spoof_machine_guid()?;

    // Spoof additional Windows identifiers that might be checked
    spoof_additional_windows_identifiers()?;

    // Clear Windows caches that might contain identifying information
    clear_windows_caches()?;

    println!("Windows机器标识符修改完成");
    Ok(())
}

// Spoof the main MachineGuid in registry
#[cfg(target_os = "windows")]
fn spoof_machine_guid() -> Result<(), Box<dyn std::error::Error>> {
    use winreg::enums::{HKEY_LOCAL_MACHINE, KEY_READ, KEY_WRITE};
    use winreg::RegKey;
    use uuid::Uuid;

    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let crypto_key = hklm.open_subkey_with_flags("SOFTWARE\\Microsoft\\Cryptography", KEY_READ | KEY_WRITE)?;
    let old_guid: String = crypto_key.get_value("MachineGuid")?;
    let new_guid = Uuid::new_v4().to_string();

    // Backup original value
    if !crypto_key.get_value::<String, _>("OriginalMachineGuid").is_ok() {
        crypto_key.set_value("OriginalMachineGuid", &old_guid)?;
    }

    crypto_key.set_value("MachineGuid", &new_guid)?;
    println!("MachineGuid从 {} 修改为 {}", old_guid, new_guid);

    Ok(())
}

// Spoof additional Windows identifiers that Augment might check
#[cfg(target_os = "windows")]
fn spoof_additional_windows_identifiers() -> Result<(), Box<dyn std::error::Error>> {
    use winreg::enums::{HKEY_LOCAL_MACHINE, KEY_READ, KEY_WRITE};
    use winreg::RegKey;
    use uuid::Uuid;

    println!("正在修改额外的Windows标识符...");

    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);

    // Spoof hardware identifiers that might be checked
    let hardware_keys = [
        ("HARDWARE\\DESCRIPTION\\System", "SystemBiosVersion"),
        ("HARDWARE\\DESCRIPTION\\System", "SystemBiosDate"),
        ("HARDWARE\\DESCRIPTION\\System\\BIOS", "BIOSVendor"),
        ("HARDWARE\\DESCRIPTION\\System\\BIOS", "BIOSVersion"),
        ("HARDWARE\\DESCRIPTION\\System\\CentralProcessor\\0", "ProcessorNameString"),
    ];

    // Generate realistic but fake values
    let fake_bios_vendor = "American Megatrends Inc.";
    let fake_bios_version = format!("P1.{}.{}", rand::random::<u8>() % 10, rand::random::<u8>() % 100);
    let fake_bios_date = "03/15/2023";
    let fake_processor = "Intel(R) Core(TM) i7-10750H CPU @ 2.60GHz";

    // Try to modify hardware identifiers (may fail on some systems due to permissions)
    if let Ok(hw_key) = hklm.open_subkey_with_flags("HARDWARE\\DESCRIPTION\\System\\BIOS", KEY_READ | KEY_WRITE) {
        let _ = hw_key.set_value("BIOSVendor", &fake_bios_vendor);
        let _ = hw_key.set_value("BIOSVersion", &fake_bios_version);
        let _ = hw_key.set_value("BIOSReleaseDate", &fake_bios_date);
        println!("已修改BIOS信息");
    }

    // Modify system identifiers
    if let Ok(sys_key) = hklm.open_subkey_with_flags("HARDWARE\\DESCRIPTION\\System", KEY_READ | KEY_WRITE) {
        let _ = sys_key.set_value("SystemBiosVersion", &fake_bios_version);
        let _ = sys_key.set_value("SystemBiosDate", &fake_bios_date);
        println!("已修改系统BIOS信息");
    }

    // Try to modify processor information
    if let Ok(cpu_key) = hklm.open_subkey_with_flags("HARDWARE\\DESCRIPTION\\System\\CentralProcessor\\0", KEY_READ | KEY_WRITE) {
        let _ = cpu_key.set_value("ProcessorNameString", &fake_processor);
        println!("已修改处理器信息");
    }

    // Modify network adapter identifiers (safer approach)
    modify_network_identifiers()?;

    println!("额外Windows标识符修改完成");
    Ok(())
}

// Modify network-related identifiers on Windows
#[cfg(target_os = "windows")]
fn modify_network_identifiers() -> Result<(), Box<dyn std::error::Error>> {
    use winreg::enums::{HKEY_LOCAL_MACHINE, KEY_READ, KEY_WRITE};
    use winreg::RegKey;

    println!("正在修改网络标识符...");

    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);

    // Try to clear network adapter cache (safer than modifying MAC addresses)
    if let Ok(net_key) = hklm.open_subkey_with_flags("SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e972-e325-11ce-bfc1-08002be10318}", KEY_READ) {
        // Enumerate network adapters and clear identifying information
        for i in 0..20 {
            let adapter_path = format!("{:04}", i);
            if let Ok(adapter_key) = net_key.open_subkey_with_flags(&adapter_path, KEY_READ | KEY_WRITE) {
                // Clear potentially identifying values (backup first)
                if let Ok(net_address) = adapter_key.get_value::<String, _>("NetworkAddress") {
                    let backup_key = format!("Original{}", "NetworkAddress");
                    if adapter_key.get_value::<String, _>(&backup_key).is_err() {
                        let _ = adapter_key.set_value(&backup_key, &net_address);
                    }
                }
            }
        }
    }

    println!("网络标识符修改完成");
    Ok(())
}

// Clear Windows caches that might contain identifying information
#[cfg(target_os = "windows")]
fn clear_windows_caches() -> Result<(), Box<dyn std::error::Error>> {
    use std::process::Command;

    println!("正在清理Windows缓存...");

    // Clear DNS cache
    let _ = Command::new("ipconfig")
        .args(["/flushdns"])
        .status();
    println!("已清理DNS缓存");

    // Clear Windows prefetch (contains application usage info)
    let _ = Command::new("del")
        .args(["/q", "/f", "C:\\Windows\\Prefetch\\*.*"])
        .status();

    // Clear temporary files that might contain identifying info
    let temp_paths = [
        "%TEMP%\\*.*",
        "%TMP%\\*.*",
        "C:\\Windows\\Temp\\*.*",
    ];

    for temp_path in &temp_paths {
        let _ = Command::new("del")
            .args(["/q", "/f", "/s", temp_path])
            .status();
    }

    // Clear Windows event logs that might contain machine info
    let event_logs = [
        "Application",
        "System",
        "Security",
        "Setup",
    ];

    for log in &event_logs {
        let _ = Command::new("wevtutil")
            .args(["cl", log])
            .status();
    }

    println!("Windows缓存清理完成");
    Ok(())
}

// Changes `/var/lib/dbus/machine-id` and `/etc/machine-id`. If reading both fails then augment resorts to `hostname`
#[cfg(target_os = "linux")]
fn spoof_linux() -> Result<(), Box<dyn std::error::Error>> {
    use std::fs::{self, File};
    use std::io::{Read};
    use uuid::Uuid;

    let paths = ["/var/lib/dbus/machine-id", "/etc/machine-id"];
    let new_id = Uuid::new_v4().to_string().replace("-", "");
    let mut old_id = String::new();
    for path in paths {
        if let Ok(mut file) = File::open(path) {
            if file.read_to_string(&mut old_id).is_err() { continue; }
            let backup_path = format!("{}.original", path);
            if !std::path::Path::new(&backup_path).exists() { fs::write(&backup_path, &old_id)?; }
            fs::write(path, &new_id)?;
            println!("Machine ID at {} changed from {} to {}", path, old_id, new_id);
            return Ok(());
        }
    }
    Err("Failed to change machine ID".into())
    // TODO: Immutable Linux Systems
    // TODO: Change hostname without altering things too much
}

// Attempts to change multiple hardware identifiers on macOS for better evasion
#[cfg(target_os = "macos")]
fn spoof_macos() -> Result<(), Box<dyn std::error::Error>> {
    use std::fs::{self, File};
    use std::io::{Read, Write};
    use std::process::Command;
    use uuid::Uuid;

    println!("正在修改macOS硬件标识符...");

    // Generate new identifiers
    let new_uuid = Uuid::new_v4().to_string();
    let new_serial = format!("C02{}", Uuid::new_v4().to_string().replace("-", "").to_uppercase()[..9].to_string());

    // 只做安全的额外欺骗 - 不会影响系统正常使用
    spoof_safe_identifiers()?;

    // Get current identifiers from IOPlatformExpertDevice
    let output = Command::new("ioreg")
        .args(["-rd1", "-c", "IOPlatformExpertDevice"])
        .output()?;

    let output_str = String::from_utf8_lossy(&output.stdout);
    let mut old_uuid = String::new();
    let mut old_serial = String::new();

    // Extract current identifiers
    for line in output_str.lines() {
        if line.contains("IOPlatformUUID") {
            if let Some(uuid_part) = line.split("\"").nth(3) {
                old_uuid = uuid_part.to_string();
            }
        }
        if line.contains("IOPlatformSerialNumber") {
            if let Some(serial_part) = line.split("\"").nth(3) {
                old_serial = serial_part.to_string();
            }
        }
    }

    if old_uuid.is_empty() {
        return Err("Failed to find current UUID".into());
    }

    // Create nvram backup (hidden location to avoid detection)
    let nvram_path = "/var/db/nvram.plist";
    let backup_path = format!("/tmp/.nvram_{}.bak", Uuid::new_v4().to_string().replace("-", "")[..8].to_string());

    if !std::path::Path::new(&backup_path).exists() && std::path::Path::new(nvram_path).exists() {
        let _ = fs::copy(nvram_path, &backup_path);
        // Make backup hidden and temporary
        let _ = Command::new("sudo")
            .args(["chflags", "hidden", &backup_path])
            .status();
    }

    // Modify multiple hardware identifiers
    let identifiers = [
        ("platform-uuid", &new_uuid),
        ("system-id", &new_uuid),
        ("hardware-uuid", &new_uuid),
    ];

    for (key, value) in &identifiers {
        let _ = Command::new("sudo")
            .args(["nvram", &format!("{}={}", key, value)])
            .status();
    }

    // Try to modify additional system identifiers (may fail silently)
    let _ = Command::new("sudo")
        .args(["nvram", &format!("platform-serial={}", new_serial)])
        .status();

    // Clear potentially identifying nvram variables
    let clear_vars = ["boot-uuid", "previous-system-uuid", "system-id"];
    for var in &clear_vars {
        let _ = Command::new("sudo")
            .args(["nvram", "-d", var])
            .status();
    }

    println!("硬件UUID从 {} 修改为 {}", old_uuid, new_uuid);
    if !old_serial.is_empty() {
        println!("序列号从 {} 修改为 {}", old_serial, new_serial);
    }

    // Clean up old backup files to avoid detection
    cleanup_backup_files()?;

    println!("注意：重启后生效。部分更改可能需要禁用SIP。");

    Ok(())
}

// Clean up any backup files that might reveal spoofing activity
#[cfg(target_os = "macos")]
fn cleanup_backup_files() -> Result<(), Box<dyn std::error::Error>> {
    use std::process::Command;
    
    let potential_backups = [
        "/var/db/nvram.plist.original",
        "/var/db/nvram.plist.backup",
        "/var/db/.nvram_backup",
    ];

    for backup_path in &potential_backups {
        if std::path::Path::new(backup_path).exists() {
            let _ = Command::new("sudo")
                .args(["rm", "-f", backup_path])
                .status();
        }
    }

    // Also clean any temp backups older than 1 day
    let _ = Command::new("find")
        .args(["/tmp", "-name", ".nvram_*.bak", "-mtime", "+1", "-delete"])
        .status();

    Ok(())
}

// 安全的标识符欺骗 - 只修改不会影响系统稳定性的标识符
#[cfg(target_os = "macos")]
fn spoof_safe_identifiers() -> Result<(), Box<dyn std::error::Error>> {
    use std::process::Command;

    println!("正在安全地修改额外标识符...");

    // 只修改一些安全的NVRAM变量，不会影响系统功能
    let safe_vars = [
        // 这些变量通常不会影响系统稳定性
        ("platform-feature", "0x00000000"),
        ("security-mode", "none"),
    ];

    for (key, value) in &safe_vars {
        let _ = Command::new("sudo")
            .args(["nvram", &format!("{}={}", key, value)])
            .status();
        println!("已修改 {}", key);
    }

    // 清理一些临时缓存文件（安全的）
    let safe_cache_files = [
        "/tmp/.DS_Store",
        "/tmp/._*",
    ];

    for cache_pattern in &safe_cache_files {
        let _ = Command::new("rm")
            .args(["-f", cache_pattern])
            .status();
    }

    println!("安全标识符修改完成");
    Ok(())
}








