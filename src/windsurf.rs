use std::fs;
use std::path::{Path, PathBuf};
use crate::logging::{print_info, print_success, print_error, print_warning};
use crate::backup::BackupManager;
use crate::ide_support::{IDEType, IDEPaths, IDEManager, PathDetector, TelemetryManager};
use crate::database::DatabaseCleaner;

/// Windsurf IDE Manager
pub struct WindsurfManager {
    backup_manager: BackupManager,
}

impl Default for WindsurfManager {
    fn default() -> Self {
        Self {
            backup_manager: BackupManager::default(),
        }
    }
}

impl WindsurfManager {
    pub fn new() -> Self {
        Self::default()
    }

    /// Detect Windsurf installation paths
    /// Supports both standard VSCode structure and Codeium structure
    pub fn detect_windsurf_paths(&self) -> Result<Vec<IDEPaths>, Box<dyn std::error::Error>> {
        let home_dir = dirs::home_dir().ok_or("Cannot determine home directory")?;
        let mut detected_paths = Vec::new();

        // Define possible base directories (ordered by priority)
        let possible_base_dirs = if cfg!(target_os = "windows") {
            let appdata = std::env::var("APPDATA").unwrap_or_default();
            if !appdata.is_empty() {
                vec![
                    PathBuf::from(&appdata).join("Windsurf"),
                    home_dir.join(".codeium/windsurf"),
                    home_dir.join(".windsurf"),
                ]
            } else {
                vec![
                    home_dir.join(".codeium/windsurf"),
                    home_dir.join(".windsurf"),
                ]
            }
        } else if cfg!(target_os = "macos") {
            vec![
                home_dir.join("Library/Application Support/Windsurf"),
                home_dir.join(".codeium/windsurf"),
                home_dir.join(".windsurf"),
            ]
        } else {
            vec![
                home_dir.join(".config/Windsurf"),
                home_dir.join(".codeium/windsurf"),
                home_dir.join(".windsurf"),
            ]
        };

        // Possible directory structures (ordered by priority)
        let possible_structures = vec![
            // Standard VSCode structure (highest priority)
            ("User/globalStorage", "extensions"),
            ("User/globalStorage", "User/extensions"),
            // Codeium possible structures
            ("globalStorage", "extensions"),
            ("data/User/globalStorage", "data/extensions"),
        ];

        for base_dir in &possible_base_dirs {
            if !base_dir.exists() {
                continue;
            }

            let path_type = if base_dir.to_string_lossy().contains("Windsurf") 
                && (base_dir.to_string_lossy().contains("AppData") 
                    || base_dir.to_string_lossy().contains("Application Support") 
                    || base_dir.to_string_lossy().contains(".config")) {
                "Standard"
            } else if base_dir.to_string_lossy().contains(".codeium") {
                "Codeium"
            } else {
                "Other"
            };

            print_info(&format!("Checking Windsurf {} path: {}", path_type, base_dir.display()));

            for (storage_path, ext_path) in &possible_structures {
                let state_db = base_dir.join(storage_path).join("state.vscdb");
                let storage_json = base_dir.join(storage_path).join("storage.json");
                let extensions = base_dir.join(ext_path);
                let global_storage = base_dir.join(storage_path);
                let workspace_storage = if storage_path.contains("User/") {
                    base_dir.join("User/workspaceStorage")
                } else {
                    base_dir.join("workspaceStorage")
                };

                // Check if key files exist
                if state_db.exists() || storage_json.exists() {
                    print_success(&format!("Found Windsurf data directory ({}): {}", path_type, base_dir.display()));
                    print_info(&format!("  - Database: {} {}", state_db.display(), 
                        if state_db.exists() { "✅" } else { "❌" }));
                    print_info(&format!("  - Storage: {} {}", storage_json.display(), 
                        if storage_json.exists() { "✅" } else { "❌" }));
                    print_info(&format!("  - Extensions: {}", extensions.display()));

                    let mut paths = IDEPaths::new();
                    paths.state_db = Some(state_db);
                    paths.storage_json = Some(storage_json);
                    paths.extensions = Some(extensions);
                    paths.config_dir = Some(base_dir.clone());
                    paths.workspace_storage = Some(workspace_storage);
                    paths.global_storage = Some(global_storage);

                    detected_paths.push(paths);
                    break; // Found valid structure for this base directory
                }
            }
        }

        if detected_paths.is_empty() {
            print_warning("No Windsurf data files found.");
            print_info("Checked path structures:");
            print_info("  Standard paths:");
            for base_dir in &possible_base_dirs {
                if base_dir.to_string_lossy().contains("Windsurf") {
                    if base_dir.exists() {
                        print_info(&format!("    ✅ Exists: {}", base_dir.display()));
                        self.list_directory_contents(base_dir);
                    } else {
                        print_info(&format!("    ❌ Not found: {}", base_dir.display()));
                    }
                }
            }
            print_info("  Codeium paths:");
            for base_dir in &possible_base_dirs {
                if base_dir.to_string_lossy().contains(".codeium") {
                    if base_dir.exists() {
                        print_info(&format!("    ✅ Exists: {}", base_dir.display()));
                        self.list_directory_contents(base_dir);
                    } else {
                        print_info(&format!("    ❌ Not found: {}", base_dir.display()));
                    }
                }
            }
        }

        Ok(detected_paths)
    }

    /// List directory contents for debugging
    fn list_directory_contents(&self, dir: &Path) {
        if let Ok(entries) = fs::read_dir(dir) {
            for entry in entries.flatten() {
                if entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false) {
                    print_info(&format!("      Subdirectory: {}", entry.file_name().to_string_lossy()));
                }
            }
        }
    }
}

impl IDEManager for WindsurfManager {
    fn ide_type(&self) -> IDEType {
        IDEType::Windsurf
    }

    fn detect_paths(&self) -> Result<IDEPaths, Box<dyn std::error::Error>> {
        let detected_paths = self.detect_windsurf_paths()?;
        
        if detected_paths.is_empty() {
            return Err("No Windsurf installations found".into());
        }

        // Return the first (highest priority) detected path
        Ok(detected_paths.into_iter().next().unwrap())
    }

    fn clean_database(&self, paths: &IDEPaths, keyword: &str) -> Result<bool, Box<dyn std::error::Error>> {
        if let Some(state_db) = &paths.state_db {
            if state_db.exists() {
                print_info(&format!("Cleaning Windsurf database: {}", state_db.display()));
                
                // Create backup before cleaning
                self.backup_manager.create_backup(state_db)?;
                
                let cleaner = DatabaseCleaner::new();
                return cleaner.clean_database(state_db, keyword);
            } else {
                print_warning(&format!("Windsurf database not found: {}", state_db.display()));
            }
        }
        
        Ok(false)
    }

    fn modify_telemetry_ids(&self, paths: &IDEPaths) -> Result<bool, Box<dyn std::error::Error>> {
        if let Some(storage_json) = &paths.storage_json {
            if storage_json.exists() {
                print_info(&format!("Modifying Windsurf telemetry IDs: {}", storage_json.display()));
                
                // Create backup before modification
                self.backup_manager.create_backup(storage_json)?;
                
                return TelemetryManager::modify_storage_json(storage_json);
            } else {
                print_warning(&format!("Windsurf storage.json not found: {}", storage_json.display()));
            }
        }
        
        Ok(false)
    }

    fn is_running(&self) -> bool {
        use sysinfo::{System, SystemExt, ProcessExt};
        let system = System::new_all();
        let process_names = self.ide_type().process_names();
        
        for (_, process) in system.processes() {
            let process_name = process.name().to_lowercase();
            if process_names.iter().any(|&name| process_name.contains(&name.to_lowercase())) {
                return true;
            }
        }
        false
    }

    fn terminate_processes(&self) -> Result<usize, Box<dyn std::error::Error>> {
        use sysinfo::{System, SystemExt, ProcessExt};
        use kill_tree::blocking::kill_tree;
        
        let system = System::new_all();
        let process_names = self.ide_type().process_names();
        let mut terminated_count = 0;

        for (pid, process) in system.processes() {
            let process_name = process.name().to_lowercase();
            let should_terminate = process_names.iter()
                .any(|&name| process_name.contains(&name.to_lowercase()));

            if should_terminate {
                print_info(&format!("Terminating Windsurf process (PID: {}): {}", pid, process_name));
                
                match kill_tree(pid.as_u32()) {
                    Ok(_) => {
                        print_success(&format!("Successfully terminated process: {}", pid));
                        terminated_count += 1;
                    },
                    Err(e) => {
                        print_error(&format!("Failed to terminate process (PID: {}): {}", pid, e));
                    }
                }
            }
        }

        print_info(&format!("Windsurf process termination complete: {} processes terminated", terminated_count));
        Ok(terminated_count)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_windsurf_manager_creation() {
        let manager = WindsurfManager::new();
        assert_eq!(manager.ide_type(), IDEType::Windsurf);
    }

    #[test]
    fn test_windsurf_process_names() {
        let manager = WindsurfManager::new();
        let process_names = manager.ide_type().process_names();
        assert!(process_names.contains(&"windsurf"));
        assert!(process_names.contains(&"Windsurf.exe"));
    }
}
