use std::fs;
use std::path::{Path, PathBuf};
use quick_xml::events::{Event, BytesEnd, BytesStart, BytesText};
use quick_xml::{Reader, Writer};
use std::io::Cursor;
use crate::logging::{print_info, print_success, print_error, print_warning};
use crate::backup::BackupManager;
use crate::ide_support::{IDEType, IDEPaths, IDEManager, IDGenerator, PathDetector};

/// JetBrains product names
pub const JETBRAINS_PRODUCTS: &[&str] = &[
    "PyCharm", "IntelliJIdea", "WebStorm", "PhpStorm", "CLion",
    "DataGrip", "GoLand", "RubyMine", "AppCode", "AndroidStudio",
    "Rider", "DataSpell"
];

/// JetBrains IDE Manager
pub struct JetBrainsManager {
    backup_manager: BackupManager,
}

impl Default for JetBrainsManager {
    fn default() -> Self {
        Self {
            backup_manager: BackupManager::default(),
        }
    }
}

impl JetBrainsManager {
    pub fn new() -> Self {
        Self::default()
    }

    /// Find all JetBrains installations
    pub fn find_installations(&self) -> Result<Vec<PathBuf>, Box<dyn std::error::Error>> {
        let base_config = PathDetector::get_base_config_dir(IDEType::JetBrains)?;
        let mut installations = Vec::new();

        if !base_config.exists() {
            print_warning(&format!("JetBrains config directory not found: {}", base_config.display()));
            return Ok(installations);
        }

        print_info(&format!("Scanning JetBrains config directory: {}", base_config.display()));

        for entry in fs::read_dir(&base_config)? {
            let entry = entry?;
            if !entry.file_type()?.is_dir() {
                continue;
            }

            let dir_name = entry.file_name();
            let dir_name_str = dir_name.to_string_lossy();

            // Check if directory matches any known JetBrains product
            for product in JETBRAINS_PRODUCTS {
                if dir_name_str.starts_with(product) {
                    installations.push(entry.path());
                    print_info(&format!("Found {} installation: {}", product, dir_name_str));
                    break;
                }
            }
        }

        Ok(installations)
    }

    /// Modify SessionID for a specific JetBrains installation
    pub fn modify_session_id(&self, config_dir: &Path, session_id: &str) -> Result<bool, Box<dyn std::error::Error>> {
        let options_dir = config_dir.join("options");
        let ide_general_file = options_dir.join("ide.general.xml");

        // Check for font configuration to protect user settings
        let font_config_file = options_dir.join("font.options.xml");
        if font_config_file.exists() {
            print_warning(&format!("Font configuration detected, skipping {} to protect user settings", 
                config_dir.file_name().unwrap_or_default().to_string_lossy()));
            return Ok(true);
        }

        // Ensure options directory exists
        fs::create_dir_all(&options_dir)?;

        let mut xml_content = if ide_general_file.exists() {
            // Backup existing file
            self.backup_manager.create_backup(&ide_general_file)?;
            print_info(&format!("Backed up existing configuration: {}", ide_general_file.display()));
            
            // Read existing XML
            fs::read_to_string(&ide_general_file)?
        } else {
            // Create new XML structure
            print_info(&format!("Creating new configuration file: {}", ide_general_file.display()));
            r#"<?xml version="1.0" encoding="UTF-8"?>
<application>
  <component name="GeneralSettings">
  </component>
</application>"#.to_string()
        };

        // Modify XML content
        xml_content = self.modify_xml_session_id(&xml_content, session_id)?;

        // Write modified content
        fs::write(&ide_general_file, xml_content)?;
        print_success(&format!("Updated SessionID to: {}", session_id));

        Ok(true)
    }

    /// Modify SessionID in XML content
    fn modify_xml_session_id(&self, xml_content: &str, session_id: &str) -> Result<String, Box<dyn std::error::Error>> {
        let mut reader = Reader::from_str(xml_content);
        reader.trim_text(true);

        let mut writer = Writer::new(Cursor::new(Vec::new()));
        let mut buf = Vec::new();
        let mut in_general_settings = false;
        let mut session_property_found = false;

        loop {
            match reader.read_event_into(&mut buf)? {
                Event::Start(ref e) => {
                    if e.name().as_ref() == b"component" {
                        // Check if this is the GeneralSettings component
                        for attr in e.attributes() {
                            let attr = attr?;
                            if attr.key.as_ref() == b"name" && attr.value.as_ref() == b"GeneralSettings" {
                                in_general_settings = true;
                                break;
                            }
                        }
                    } else if e.name().as_ref() == b"property" && in_general_settings {
                        // Check if this is the session ID property
                        let mut is_session_property = false;
                        let mut new_elem = BytesStart::new("property");
                        
                        for attr in e.attributes() {
                            let attr = attr?;
                            if attr.key.as_ref() == b"name" && attr.value.as_ref() == b"augment.session.id" {
                                is_session_property = true;
                                session_property_found = true;
                            }
                            
                            if is_session_property && attr.key.as_ref() == b"value" {
                                new_elem.push_attribute(("name", "augment.session.id"));
                                new_elem.push_attribute(("value", session_id));
                            } else {
                                new_elem.push_attribute((attr.key.as_ref(), attr.value.as_ref()));
                            }
                        }
                        
                        if is_session_property {
                            writer.write_event(Event::Start(new_elem))?;
                        } else {
                            writer.write_event(Event::Start(e.clone()))?;
                        }
                    } else {
                        writer.write_event(Event::Start(e.clone()))?;
                    }
                },
                Event::End(ref e) => {
                    if e.name().as_ref() == b"component" && in_general_settings {
                        // Add session property if not found
                        if !session_property_found {
                            let mut session_elem = BytesStart::new("property");
                            session_elem.push_attribute(("name", "augment.session.id"));
                            session_elem.push_attribute(("value", session_id));
                            writer.write_event(Event::Empty(session_elem))?;
                        }
                        in_general_settings = false;
                    }
                    writer.write_event(Event::End(e.clone()))?;
                },
                Event::Eof => break,
                e => writer.write_event(e)?,
            }
            buf.clear();
        }

        let result = writer.into_inner().into_inner();
        Ok(String::from_utf8(result)?)
    }

    /// Modify SessionIDs for all JetBrains installations
    pub fn modify_all_session_ids(&self, custom_session_id: Option<String>) -> Result<bool, Box<dyn std::error::Error>> {
        let session_id = custom_session_id.unwrap_or_else(|| IDGenerator::generate_session_id());
        print_info(&format!("Using SessionID: {}", session_id));

        let installations = self.find_installations()?;

        if installations.is_empty() {
            print_warning("No JetBrains installations found");
            return Ok(false);
        }

        let mut success_count = 0;
        let total_count = installations.len();

        for config_dir in installations {
            print_info(&format!("Processing: {}", config_dir.file_name().unwrap_or_default().to_string_lossy()));
            
            match self.modify_session_id(&config_dir, &session_id) {
                Ok(true) => {
                    success_count += 1;
                    print_success(&format!("Successfully processed: {}", config_dir.display()));
                },
                Ok(false) => {
                    print_warning(&format!("Skipped: {}", config_dir.display()));
                },
                Err(e) => {
                    print_error(&format!("Failed to process {}: {}", config_dir.display(), e));
                }
            }
        }

        print_info(&format!("Processing complete: {}/{} configurations modified successfully", success_count, total_count));

        if success_count > 0 {
            print_success("JetBrains SessionID modification completed!");
            print_info("Notes:");
            print_info("1. Please restart JetBrains products for changes to take effect");
            print_info("2. Original configuration files have been backed up");
            print_info("3. To restore, delete the modified files or use backup files");
            Ok(true)
        } else {
            print_error("All JetBrains SessionID modifications failed");
            Ok(false)
        }
    }
}

impl IDEManager for JetBrainsManager {
    fn ide_type(&self) -> IDEType {
        IDEType::JetBrains
    }

    fn detect_paths(&self) -> Result<IDEPaths, Box<dyn std::error::Error>> {
        let base_config = PathDetector::get_base_config_dir(IDEType::JetBrains)?;
        
        let mut paths = IDEPaths::new();
        paths.config_dir = Some(base_config);
        
        Ok(paths)
    }

    fn clean_database(&self, _paths: &IDEPaths, _keyword: &str) -> Result<bool, Box<dyn std::error::Error>> {
        print_info("JetBrains products do not require database cleaning, skipping this step");
        Ok(true)
    }

    fn modify_telemetry_ids(&self, _paths: &IDEPaths) -> Result<bool, Box<dyn std::error::Error>> {
        self.modify_all_session_ids(None)
    }

    fn is_running(&self) -> bool {
        use sysinfo::{System, SystemExt, ProcessExt};
        let system = System::new_all();
        let process_names = self.ide_type().process_names();
        
        for (_, process) in system.processes() {
            let process_name = process.name().to_lowercase();
            if process_names.iter().any(|&name| process_name.contains(&name.to_lowercase())) {
                return true;
            }
        }
        false
    }

    fn terminate_processes(&self) -> Result<usize, Box<dyn std::error::Error>> {
        use sysinfo::{System, SystemExt, ProcessExt, Pid};
        use kill_tree::blocking::kill_tree;
        
        let system = System::new_all();
        let process_names = self.ide_type().process_names();
        let mut terminated_count = 0;

        for (pid, process) in system.processes() {
            let process_name = process.name().to_lowercase();
            let should_terminate = process_names.iter()
                .any(|&name| process_name.contains(&name.to_lowercase()));

            if should_terminate {
                print_info(&format!("Terminating JetBrains process (PID: {}): {}", pid, process_name));
                
                match kill_tree(pid.as_u32()) {
                    Ok(_) => {
                        print_success(&format!("Successfully terminated process: {}", pid));
                        terminated_count += 1;
                    },
                    Err(e) => {
                        print_error(&format!("Failed to terminate process (PID: {}): {}", pid, e));
                    }
                }
            }
        }

        print_info(&format!("JetBrains process termination complete: {} processes terminated", terminated_count));
        Ok(terminated_count)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_jetbrains_manager_creation() {
        let manager = JetBrainsManager::new();
        assert_eq!(manager.ide_type(), IDEType::JetBrains);
    }

    #[test]
    fn test_xml_session_id_modification() {
        let manager = JetBrainsManager::new();
        let xml_content = r#"<?xml version="1.0" encoding="UTF-8"?>
<application>
  <component name="GeneralSettings">
  </component>
</application>"#;

        let session_id = "test-session-id";
        let result = manager.modify_xml_session_id(xml_content, session_id);
        
        assert!(result.is_ok());
        let modified_xml = result.unwrap();
        assert!(modified_xml.contains("augment.session.id"));
        assert!(modified_xml.contains(session_id));
    }
}
