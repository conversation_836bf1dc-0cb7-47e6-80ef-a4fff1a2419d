use std::path::Path;
use rusqlite::{Connection, Result as SqliteResult};
use crate::logging::{print_info, print_success, print_error, print_warning};
use crate::backup::BackupManager;

/// Database cleaning errors
#[derive(thiserror::Error, Debug)]
pub enum DatabaseError {
    #[error("Database file not found: {0}")]
    FileNotFound(String),
    #[error("SQLite error: {0}")]
    SqliteError(#[from] rusqlite::Error),
    #[error("Backup error: {0}")]
    BackupError(#[from] crate::backup::BackupError),
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
}

/// Enhanced database cleaner with verification and rollback capabilities
pub struct DatabaseCleaner {
    backup_manager: BackupManager,
}

impl Default for DatabaseCleaner {
    fn default() -> Self {
        Self {
            backup_manager: BackupManager::default(),
        }
    }
}

impl DatabaseCleaner {
    pub fn new() -> Self {
        Self::default()
    }

    /// Clean database by removing entries containing the specified keyword
    pub fn clean_database<P: AsRef<Path>>(&self, db_path: P, keyword: &str) -> Result<bool, Box<dyn std::error::Error>> {
        let db_path = db_path.as_ref();
        
        if !db_path.exists() {
            self.print_troubleshooting_info(db_path);
            return Err(DatabaseError::FileNotFound(db_path.display().to_string()).into());
        }

        print_info(&format!("Attempting to clean database: {}", db_path.display()));
        print_info(&format!("Target keyword: '{}'", keyword));

        // Create backup before cleaning
        print_info("Creating database backup...");
        let backup_path = self.backup_manager.create_backup(db_path)?;
        print_success(&format!("Database backup created: {}", backup_path.display()));

        // Perform cleaning with rollback capability
        match self.clean_database_internal(db_path, keyword) {
            Ok(result) => {
                print_success("Database cleaning completed successfully");
                Ok(result)
            },
            Err(e) => {
                print_error(&format!("Database cleaning failed: {}", e));
                print_warning("Attempting to restore from backup...");
                
                match self.backup_manager.restore_from_backup(db_path) {
                    Ok(_) => {
                        print_success("Database successfully restored from backup");
                    },
                    Err(restore_e) => {
                        print_error(&format!("Failed to restore from backup: {}", restore_e));
                        print_error(&format!("Original database {} may be corrupted", db_path.display()));
                        print_error(&format!("Backup file location: {}", backup_path.display()));
                    }
                }
                
                Err(e)
            }
        }
    }

    /// Internal database cleaning implementation
    fn clean_database_internal<P: AsRef<Path>>(&self, db_path: P, keyword: &str) -> Result<bool, DatabaseError> {
        let db_path = db_path.as_ref();
        
        print_info(&format!("Connecting to database: {}", db_path.display()));
        let conn = Connection::open(db_path)?;
        print_success("Successfully connected to database");

        // Find entries to be deleted
        let query_select = "SELECT key FROM ItemTable WHERE key LIKE ?";
        let like_pattern = format!("%{}%", keyword);
        
        print_info(&format!("Searching for entries containing keyword '{}'...", keyword));
        let mut stmt = conn.prepare(query_select)?;
        let entries_to_delete: Vec<String> = stmt.query_map([&like_pattern], |row| {
            Ok(row.get::<_, String>(0)?)
        })?.collect::<SqliteResult<Vec<_>>>()?;

        let num_entries_to_delete = entries_to_delete.len();

        if num_entries_to_delete == 0 {
            print_success(&format!("No entries containing keyword '{}' found. Database is already clean.", keyword));
            return Ok(true);
        }

        print_info(&format!("Found {} entries containing '{}':", num_entries_to_delete, keyword));
        for (i, entry) in entries_to_delete.iter().enumerate() {
            if i < 5 {
                print_info(&format!("  - {}", entry));
            } else if i == 5 {
                print_info(&format!("  ... and {} more entries", num_entries_to_delete - 5));
                break;
            }
        }

        // Delete the entries
        let query_delete = "DELETE FROM ItemTable WHERE key LIKE ?";
        print_info(&format!("Deleting entries containing '{}'...", keyword));
        let deleted_rows = conn.execute(query_delete, [&like_pattern])?;

        // Verify deletion
        if deleted_rows == num_entries_to_delete {
            print_success(&format!("Successfully deleted {} entries containing '{}'", deleted_rows, keyword));
        } else {
            print_warning(&format!("Expected to delete {} entries, but database reported {} deletions", 
                num_entries_to_delete, deleted_rows));
            if deleted_rows > 0 {
                print_success(&format!("Partial success: deleted {} entries", deleted_rows));
            } else {
                print_error("No entries were deleted despite finding matching entries");
                return Ok(false);
            }
        }

        // Verify the cleaning was successful
        self.verify_cleaning(&conn, keyword)?;

        Ok(true)
    }

    /// Verify that cleaning was successful
    fn verify_cleaning(&self, conn: &Connection, keyword: &str) -> Result<(), DatabaseError> {
        let query_verify = "SELECT COUNT(*) FROM ItemTable WHERE key LIKE ?";
        let like_pattern = format!("%{}%", keyword);
        
        let remaining_count: i64 = conn.query_row(query_verify, [&like_pattern], |row| {
            Ok(row.get(0)?)
        })?;

        if remaining_count == 0 {
            print_success("Verification successful: No matching entries remain in database");
        } else {
            print_warning(&format!("Verification warning: {} entries containing '{}' still remain", 
                remaining_count, keyword));
        }

        Ok(())
    }

    /// Print troubleshooting information when database is not found
    fn print_troubleshooting_info(&self, db_path: &Path) {
        print_error(&format!("Database file not found: {}", db_path.display()));
        print_info("Troubleshooting suggestions:");
        print_info("1. Ensure the IDE has been installed and run at least once");
        print_info("2. Check that the IDE is completely closed");
        print_info("3. Verify user permissions to access the configuration directory");

        // Check parent directory
        if let Some(parent_dir) = db_path.parent() {
            if parent_dir.exists() {
                print_info(&format!("Parent directory exists: {}", parent_dir.display()));
                match std::fs::read_dir(parent_dir) {
                    Ok(entries) => {
                        let files: Vec<_> = entries.filter_map(|e| e.ok()).collect();
                        if !files.is_empty() {
                            print_info("Files in parent directory:");
                            for (i, file) in files.iter().enumerate() {
                                if i < 10 {
                                    print_info(&format!("  - {}", file.file_name().to_string_lossy()));
                                } else {
                                    print_info(&format!("  ... and {} more files", files.len() - 10));
                                    break;
                                }
                            }
                        } else {
                            print_warning("Parent directory is empty");
                        }
                    },
                    Err(_) => {
                        print_warning("Cannot access parent directory contents (insufficient permissions)");
                    }
                }
            } else {
                print_error(&format!("Parent directory does not exist: {}", parent_dir.display()));
            }
        }
    }

    /// Get database statistics
    pub fn get_database_stats<P: AsRef<Path>>(&self, db_path: P) -> Result<DatabaseStats, DatabaseError> {
        let db_path = db_path.as_ref();
        
        if !db_path.exists() {
            return Err(DatabaseError::FileNotFound(db_path.display().to_string()));
        }

        let conn = Connection::open(db_path)?;
        
        let total_entries: i64 = conn.query_row("SELECT COUNT(*) FROM ItemTable", [], |row| {
            Ok(row.get(0)?)
        })?;

        let file_size = std::fs::metadata(db_path)?.len();

        Ok(DatabaseStats {
            total_entries,
            file_size,
            file_path: db_path.to_path_buf(),
        })
    }
}

/// Database statistics
#[derive(Debug)]
pub struct DatabaseStats {
    pub total_entries: i64,
    pub file_size: u64,
    pub file_path: std::path::PathBuf,
}

impl DatabaseStats {
    pub fn print_stats(&self) {
        print_info(&format!("Database Statistics for: {}", self.file_path.display()));
        print_info(&format!("  Total entries: {}", self.total_entries));
        print_info(&format!("  File size: {} bytes ({:.2} KB)", self.file_size, self.file_size as f64 / 1024.0));
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    use std::fs::File;

    #[test]
    fn test_database_cleaning() {
        let dir = tempdir().unwrap();
        let db_path = dir.path().join("test.vscdb");
        
        // Create test database
        let conn = Connection::open(&db_path).unwrap();
        conn.execute("CREATE TABLE IF NOT EXISTS ItemTable (key TEXT PRIMARY KEY, value BLOB)", []).unwrap();
        
        let test_data = [
            ("storage.testKey1", b"testValue1"),
            ("augment.testKey2", b"testValue2"),
            ("another.augment.key", b"testValue3"),
            ("noKeywordHere", b"testValue4"),
        ];
        
        for (key, value) in &test_data {
            conn.execute("INSERT OR IGNORE INTO ItemTable VALUES (?, ?)", [key, value]).unwrap();
        }
        conn.close().unwrap();

        // Test cleaning
        let cleaner = DatabaseCleaner::new();
        let result = cleaner.clean_database(&db_path, "augment").unwrap();
        assert!(result);

        // Verify cleaning
        let conn = Connection::open(&db_path).unwrap();
        let remaining_count: i64 = conn.query_row("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'", [], |row| {
            Ok(row.get(0).unwrap())
        }).unwrap();
        assert_eq!(remaining_count, 0);
    }

    #[test]
    fn test_database_stats() {
        let dir = tempdir().unwrap();
        let db_path = dir.path().join("test.vscdb");
        
        // Create test database
        let conn = Connection::open(&db_path).unwrap();
        conn.execute("CREATE TABLE IF NOT EXISTS ItemTable (key TEXT PRIMARY KEY, value BLOB)", []).unwrap();
        conn.execute("INSERT INTO ItemTable VALUES ('test', 'value')", []).unwrap();
        conn.close().unwrap();

        let cleaner = DatabaseCleaner::new();
        let stats = cleaner.get_database_stats(&db_path).unwrap();
        
        assert_eq!(stats.total_entries, 1);
        assert!(stats.file_size > 0);
    }
}
